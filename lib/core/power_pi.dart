// // // import 'dart:convert';

// // // import 'package:file_picker/file_picker.dart';
// // // import 'package:flutter/material.dart';
// // // import 'package:http/http.dart' as http;
// // // import 'package:url_launcher/url_launcher.dart';

// // // const String tenantId = "a7d1894d-7354-4c76-9ca8-fe9a3d434e6e";
// // // const String clientId = "88be577d-070e-41e7-8dc9-9ab39ae1d3fa";
// // // const String clientSecret = "****************************************";
// // // // Function to authenticate and get access token
// // // Future<String> authenticate() async {
// // //   final uri = Uri.parse(
// // //       'https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token');
// // //   final response = await http.post(
// // //     uri,
// // //     headers: {'Content-Type': 'application/x-www-form-urlencoded'},
// // //     body: {
// // //       'client_id': clientId,
// // //       'client_secret': clientSecret,
// // //       'scope': 'https://graph.microsoft.com/.default',
// // //       'grant_type': 'client_credentials',
// // //     },
// // //   );
// // //   print(response.body);
// // //   if (response.statusCode == 200) {
// // //     final responseBody = json.decode(response.body);
// // //     return responseBody['access_token'];
// // //   } else {
// // //     throw Exception('Failed to authenticate: ${response.statusCode}');
// // //   }
// // // }

// // // // Function to upload an Excel file to Power BI
// // // Future<void> uploadExcelFile(
// // //     String accessToken, String filePath, String groupId) async {
// // //   final uri =
// // //       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/imports');
// // //   final request = http.MultipartRequest('POST', uri)
// // //     ..headers['Authorization'] = 'Bearer $accessToken'
// // //     ..files.add(await http.MultipartFile.fromPath('file', filePath));

// // //   final response = await request.send();

// // //   if (response.statusCode == 200) {
// // //     print('Excel file uploaded successfully');
// // //   } else {
// // //     throw Exception('Failed to upload Excel file: ${response.statusCode}');
// // //   }
// // // }

// // // // Function to create a dataset in Power BI
// // // Future<void> createDataset(
// // //     String accessToken, String datasetName, String groupId) async {
// // //   final uri =
// // //       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/datasets');
// // //   final response = await http.post(
// // //     uri,
// // //     headers: {
// // //       'Authorization': 'Bearer $accessToken',
// // //       'Content-Type': 'application/json',
// // //     },
// // //     body: json.encode({
// // //       'name': datasetName,
// // //       // Add more dataset parameters if necessary
// // //     }),
// // //   );

// // //   if (response.statusCode == 201) {
// // //     print('Dataset created successfully');
// // //   } else {
// // //     throw Exception('Failed to create dataset: ${response.statusCode}');
// // //   }
// // // }

// // // // Function to create a report in Power BI
// // // Future<void> createReport(
// // //     String accessToken, String datasetId, String groupId) async {
// // //   final uri =
// // //       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/reports');
// // //   final response = await http.post(
// // //     uri,
// // //     headers: {
// // //       'Authorization': 'Bearer $accessToken',
// // //       'Content-Type': 'application/json',
// // //     },
// // //     body: json.encode({
// // //       'datasetId': datasetId,
// // //       'name': 'Sample Report',
// // //     }),
// // //   );

// // //   if (response.statusCode == 201) {
// // //     print('Report created successfully');
// // //   } else {
// // //     throw Exception('Failed to create report: ${response.statusCode}');
// // //   }
// // // }

// // // // Function to get embed token for the report
// // // Future<String> getEmbedToken(String accessToken, String reportId) async {
// // //   final uri = Uri.parse(
// // //       'https://api.powerbi.com/v1.0/myorg/reports/$reportId/GenerateToken');
// // //   final response = await http.post(
// // //     uri,
// // //     headers: {
// // //       'Authorization': 'Bearer $accessToken',
// // //       'Content-Type': 'application/json',
// // //     },
// // //     body: json.encode({
// // //       'accessLevel': 'view', // Options: 'view', 'edit'
// // //     }),
// // //   );

// // //   if (response.statusCode == 200) {
// // //     final embedToken = json.decode(response.body)['token'];
// // //     print('Embed token: $embedToken');
// // //     return embedToken;
// // //   } else {
// // //     throw Exception('Failed to get embed token: ${response.statusCode}');
// // //   }
// // // }

// // // // Function to launch the URL in the browser
// // // Future<void> launchPowerBIReport(
// // //     String embedUrl, String embedToken, String reportId, String groupId) async {
// // //   final url =
// // //       '$embedUrl?reportId=$reportId&groupId=$groupId&access_token=$embedToken';
// // //   if (await canLaunch(url)) {
// // //     await launch(url);
// // //   } else {
// // //     throw 'Could not launch $url';
// // //   }
// // // }

// // // // PowerBIReportPage widget to display the report
// // // class PowerBIReportPage extends StatelessWidget {
// // //   final String embedUrl;
// // //   final String embedToken;
// // //   final String reportId;
// // //   final String groupId;

// // //   const PowerBIReportPage({
// // //     super.key,
// // //     required this.embedUrl,
// // //     required this.embedToken,
// // //     required this.reportId,
// // //     required this.groupId,
// // //   });

// // //   @override
// // //   Widget build(BuildContext context) {
// // //     return Scaffold(
// // //       appBar: AppBar(title: Text('Power BI Report')),
// // //       body: Center(
// // //         child: ElevatedButton(
// // //           onPressed: () async {
// // //             await launchPowerBIReport(embedUrl, embedToken, reportId, groupId);
// // //           },
// // //           child: Text('Open Power BI Report'),
// // //         ),
// // //       ),
// // //     );
// // //   }
// // // }

// // // // Main function to tie everything together
// // // void main() async {
// // //   WidgetsFlutterBinding.ensureInitialized();

// // //   // Authenticate and get the access token
// // //   final accessToken = await authenticate();

// // //   // Pick an Excel file using file_picker
// // //   FilePickerResult? result = await FilePicker.platform
// // //       .pickFiles(type: FileType.custom, allowedExtensions: ['xlsx']);
// // //   if (result == null) {
// // //     print("No file selected.");
// // //     return;
// // //   }

// // //   // Get the selected file path
// // //   final filePath = result.files.single.path!;

// // //   // Run the app and prompt for groupId and reportId dynamically after the app is built
// // //   runApp(MyApp(accessToken, filePath));
// // // }

// // // // Main app to prompt user input
// // // class MyApp extends StatelessWidget {
// // //   final String accessToken;
// // //   final String filePath;

// // //   const MyApp(this.accessToken, this.filePath, {super.key});

// // //   @override
// // //   Widget build(BuildContext context) {
// // //     return MaterialApp(
// // //       home: UserInputPage(accessToken: accessToken, filePath: filePath),
// // //     );
// // //   }
// // // }

// // // // Page to get groupId and reportId from user input
// // // class UserInputPage extends StatelessWidget {
// // //   final String accessToken;
// // //   final String filePath;

// // //   const UserInputPage(
// // //       {super.key, required this.accessToken, required this.filePath});

// // //   @override
// // //   Widget build(BuildContext context) {
// // //     return Scaffold(
// // //       appBar: AppBar(title: Text('Power BI Integration')),
// // //       body: Center(
// // //         child: ElevatedButton(
// // //           onPressed: () async {
// // //             // Get groupId and reportId from user
// // //             String groupId =
// // //                 await _promptUserInput(context, "Enter Power BI Group ID:");
// // //             String reportId =
// // //                 await _promptUserInput(context, "Enter Power BI Report ID:");

// // //             try {
// // //               // Upload Excel file
// // //               await uploadExcelFile(accessToken, filePath, groupId);

// // //               // Create dataset (replace with actual datasetId)
// // //               String datasetName = 'Sample Dataset';
// // //               await createDataset(accessToken, datasetName, groupId);

// // //               // Create report (replace with actual datasetId)
// // //               String datasetId =
// // //                   'your-dataset-id'; // Dataset ID from the created dataset
// // //               await createReport(accessToken, datasetId, groupId);

// // //               // Get the embed token for the report
// // //               final embedToken = await getEmbedToken(accessToken, reportId);

// // //               // Embed the report in the app
// // //               Navigator.push(
// // //                 context,
// // //                 MaterialPageRoute(
// // //                   builder: (context) => PowerBIReportPage(
// // //                     embedUrl:
// // //                         'https://app.powerbi.com/reportEmbed', // Provide embed URL here
// // //                     embedToken: embedToken,
// // //                     reportId: reportId,
// // //                     groupId: groupId,
// // //                   ),
// // //                 ),
// // //               );
// // //             } catch (e) {
// // //               print('Error: $e');
// // //             }
// // //           },
// // //           child: Text('Start Power BI Integration'),
// // //         ),
// // //       ),
// // //     );
// // //   }

// // //   // Function to prompt user for input
// // //   Future<String> _promptUserInput(BuildContext context, String prompt) async {
// // //     final TextEditingController controller = TextEditingController();
// // //     String input = '';
// // //     await showDialog<String>(
// // //       context: context,
// // //       builder: (BuildContext context) {
// // //         return AlertDialog(
// // //           title: Text(prompt),
// // //           content: TextField(
// // //             controller: controller,
// // //             decoration: InputDecoration(hintText: 'Enter here'),
// // //           ),
// // //           actions: <Widget>[
// // //             TextButton(
// // //               onPressed: () {
// // //                 input = controller.text;
// // //                 Navigator.of(context).pop(input);
// // //               },
// // //               child: Text('OK'),
// // //             ),
// // //           ],
// // //         );
// // //       },
// // //     );
// // //     return input;
// // //   }
// // // }

// // import 'dart:convert';
// // import 'package:file_picker/file_picker.dart';
// // import 'package:flutter/material.dart';
// // import 'package:http/http.dart' as http;
// // import 'package:url_launcher/url_launcher.dart';

// // const String tenantId = "a7d1894d-7354-4c76-9ca8-fe9a3d434e6e";
// // const String clientId = "88be577d-070e-41e7-8dc9-9ab39ae1d3fa";
// // const String clientSecret = "****************************************";

// // // Function to authenticate and get access token
// // Future<String> authenticate() async {
// //   final uri = Uri.parse(
// //       'https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token');
// //   final response = await http.post(
// //     uri,
// //     headers: {'Content-Type': 'application/x-www-form-urlencoded'},
// //     body: {
// //       'client_id': clientId,
// //       'client_secret': clientSecret,
// //       'scope': 'https://graph.microsoft.com/.default',
// //       'grant_type': 'client_credentials',
// //     },
// //   );
// //   print(response.body);
// //   if (response.statusCode == 200) {
// //     final responseBody = json.decode(response.body);
// //     return responseBody['access_token'];
// //   } else {
// //     throw Exception('Failed to authenticate: ${response.statusCode}');
// //   }
// // }

// // // Function to upload an Excel file to Power BI
// // Future<void> uploadExcelFile(
// //     String accessToken, String filePath, String groupId) async {
// //   final uri =
// //       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/imports');
// //   final request = http.MultipartRequest('POST', uri)
// //     ..headers['Authorization'] = 'Bearer $accessToken'
// //     ..files.add(await http.MultipartFile.fromPath('file', filePath));

// //   final response = await request.send();

// //   if (response.statusCode == 200) {
// //     print('Excel file uploaded successfully');
// //   } else {
// //     throw Exception('Failed to upload Excel file: ${response.statusCode}');
// //   }
// // }

// // // Function to create a dataset in Power BI
// // Future<void> createDataset(
// //     String accessToken, String datasetName, String groupId) async {
// //   final uri =
// //       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/datasets');
// //   final response = await http.post(
// //     uri,
// //     headers: {
// //       'Authorization': 'Bearer $accessToken',
// //       'Content-Type': 'application/json',
// //     },
// //     body: json.encode({
// //       'name': datasetName,
// //       // Add more dataset parameters if necessary
// //     }),
// //   );

// //   if (response.statusCode == 201) {
// //     print('Dataset created successfully');
// //   } else {
// //     throw Exception('Failed to create dataset: ${response.statusCode}');
// //   }
// // }

// // // Function to create a report in Power BI
// // Future<void> createReport(
// //     String accessToken, String datasetId, String groupId) async {
// //   final uri =
// //       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/reports');
// //   final response = await http.post(
// //     uri,
// //     headers: {
// //       'Authorization': 'Bearer $accessToken',
// //       'Content-Type': 'application/json',
// //     },
// //     body: json.encode({
// //       'datasetId': datasetId,
// //       'name': 'Sample Report',
// //     }),
// //   );

// //   if (response.statusCode == 201) {
// //     print('Report created successfully');
// //   } else {
// //     throw Exception('Failed to create report: ${response.statusCode}');
// //   }
// // }

// // // Function to get embed token for the report
// // Future<String> getEmbedToken(String accessToken, String reportId) async {
// //   final uri = Uri.parse(
// //       'https://api.powerbi.com/v1.0/myorg/reports/$reportId/GenerateToken');
// //   final response = await http.post(
// //     uri,
// //     headers: {
// //       'Authorization': 'Bearer $accessToken',
// //       'Content-Type': 'application/json',
// //     },
// //     body: json.encode({
// //       'accessLevel': 'view', // Options: 'view', 'edit'
// //     }),
// //   );

// //   if (response.statusCode == 200) {
// //     final embedToken = json.decode(response.body)['token'];
// //     print('Embed token: $embedToken');
// //     return embedToken;
// //   } else {
// //     throw Exception('Failed to get embed token: ${response.statusCode}');
// //   }
// // }

// // // Function to launch the URL in the browser
// // Future<void> launchPowerBIReport(
// //     String embedUrl, String embedToken, String reportId, String groupId) async {
// //   final url =
// //       '$embedUrl?reportId=$reportId&groupId=$groupId&access_token=$embedToken';
// //   if (await canLaunch(url)) {
// //     await launch(url);
// //   } else {
// //     throw 'Could not launch $url';
// //   }
// // }

// // // PowerBIReportPage widget to display the report
// // class PowerBIReportPage extends StatelessWidget {
// //   final String embedUrl;
// //   final String embedToken;
// //   final String reportId;
// //   final String groupId;

// //   const PowerBIReportPage({
// //     super.key,
// //     required this.embedUrl,
// //     required this.embedToken,
// //     required this.reportId,
// //     required this.groupId,
// //   });

// //   @override
// //   Widget build(BuildContext context) {
// //     return Scaffold(
// //       appBar: AppBar(title: Text('Power BI Report')),
// //       body: Center(
// //         child: ElevatedButton(
// //           onPressed: () async {
// //             await launchPowerBIReport(embedUrl, embedToken, reportId, groupId);
// //           },
// //           child: Text('Open Power BI Report'),
// //         ),
// //       ),
// //     );
// //   }
// // }

// // // Main function to tie everything together
// // void main() async {
// //   WidgetsFlutterBinding.ensureInitialized();

// //   // Authenticate and get the access token
// //   final accessToken = await authenticate();

// //   // Pick an Excel file using file_picker
// //   FilePickerResult? result = await FilePicker.platform
// //       .pickFiles(type: FileType.custom, allowedExtensions: ['xlsx']);
// //   if (result == null) {
// //     print("No file selected.");
// //     return;
// //   }

// //   // Get the selected file path
// //   final filePath = result.files.single.path!;

// //   // Run the app and prompt for groupId and reportId dynamically after the app is built
// //   runApp(MyApp(accessToken, filePath));
// // }

// // // Main app to prompt user input
// // class MyApp extends StatelessWidget {
// //   final String accessToken;
// //   final String filePath;

// //   const MyApp(this.accessToken, this.filePath, {super.key});

// //   @override
// //   Widget build(BuildContext context) {
// //     return MaterialApp(
// //       home: UserInputPage(accessToken: accessToken, filePath: filePath),
// //     );
// //   }
// // }

// // // Page to get groupId and reportId from user input after file upload
// // class UserInputPage extends StatelessWidget {
// //   final String accessToken;
// //   final String filePath;

// //   const UserInputPage(
// //       {super.key, required this.accessToken, required this.filePath});

// //   @override
// //   Widget build(BuildContext context) {
// //     return Scaffold(
// //       appBar: AppBar(title: Text('Power BI Integration')),
// //       body: Center(
// //         child: ElevatedButton(
// //           onPressed: () async {
// //             try {
// //               // Get groupId dynamically after file upload
// //               String groupId =
// //                   await _promptUserInput(context, "Enter Power BI Group ID:");

// //               // Upload Excel file to Power BI first
// //               await uploadExcelFile(accessToken, filePath, groupId);

// //               // Now get reportId from the user
// //               String reportId =
// //                   await _promptUserInput(context, "Enter Power BI Report ID:");

// //               // Create dataset (replace with actual datasetId)
// //               String datasetName = 'Sample Dataset';
// //               await createDataset(accessToken, datasetName, groupId);

// //               // Create report (replace with actual datasetId)
// //               String datasetId =
// //                   'your-dataset-id'; // Dataset ID from the created dataset
// //               await createReport(accessToken, datasetId, groupId);

// //               // Get the embed token for the report
// //               final embedToken = await getEmbedToken(accessToken, reportId);

// //               // Embed the report in the app
// //               Navigator.push(
// //                 context,
// //                 MaterialPageRoute(
// //                   builder: (context) => PowerBIReportPage(
// //                     embedUrl:
// //                         'https://app.powerbi.com/reportEmbed', // Provide embed URL here
// //                     embedToken: embedToken,
// //                     reportId: reportId,
// //                     groupId: groupId,
// //                   ),
// //                 ),
// //               );
// //             } catch (e) {
// //               print('Error: $e');
// //             }
// //           },
// //           child: Text('Start Power BI Integration'),
// //         ),
// //       ),
// //     );
// //   }

// //   // Function to prompt user for input
// //   Future<String> _promptUserInput(BuildContext context, String prompt) async {
// //     final TextEditingController controller = TextEditingController();
// //     String input = '';
// //     await showDialog<String>(
// //       context: context,
// //       builder: (BuildContext context) {
// //         return AlertDialog(
// //           title: Text(prompt),
// //           content: TextField(
// //             controller: controller,
// //             decoration: InputDecoration(hintText: 'Enter here'),
// //           ),
// //           actions: <Widget>[
// //             TextButton(
// //               onPressed: () {
// //                 input = controller.text;
// //                 Navigator.of(context).pop(input);
// //               },
// //               child: Text('OK'),
// //             ),
// //           ],
// //         );
// //       },
// //     );
// //     return input;
// //   }
// // }

// import 'dart:convert';
// import 'package:file_picker/file_picker.dart';
// import 'package:flutter/material.dart';
// import 'package:http/http.dart' as http;
// import 'package:url_launcher/url_launcher.dart';

// const String tenantId = "a7d1894d-7354-4c76-9ca8-fe9a3d434e6e";
// const String clientId = "88be577d-070e-41e7-8dc9-9ab39ae1d3fa";
// const String clientSecret = "****************************************";

// // Function to authenticate and get access token
// Future<String> authenticate() async {
//   final uri = Uri.parse(
//       'https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token');
//   final response = await http.post(
//     uri,
//     headers: {'Content-Type': 'application/x-www-form-urlencoded'},
//     body: {
//       'client_id': clientId,
//       'client_secret': clientSecret,
//       'scope': 'https://graph.microsoft.com/.default',
//       'grant_type': 'client_credentials',
//     },
//   );

//   if (response.statusCode == 200) {
//     final responseBody = json.decode(response.body);
//     return responseBody['access_token'];
//   } else {
//     throw Exception('Failed to authenticate: ${response.statusCode}');
//   }
// }

// // Function to get available groups (workspaces)
// Future<List<dynamic>> getAvailableGroups(String accessToken) async {
//   final uri = Uri.parse('https://graph.microsoft.com/v1.0/me/joinedGroups');
//   final response = await http.get(
//     uri,
//     headers: {'Authorization': 'Bearer $accessToken'},
//   );

//   if (response.statusCode == 200) {
//     final responseBody = json.decode(response.body);
//     return responseBody['value']; // List of groups
//   } else {
//     throw Exception('Failed to retrieve groups: ${response.statusCode}');
//   }
// }

// // Function to get available reports in a group
// Future<List<dynamic>> getReportsInGroup(String accessToken, String groupId) async {
//   final uri = Uri.parse(
//       'https://api.powerbi.com/v1.0/myorg/groups/$groupId/reports');
//   final response = await http.get(
//     uri,
//     headers: {'Authorization': 'Bearer $accessToken'},
//   );

//   if (response.statusCode == 200) {
//     final responseBody = json.decode(response.body);
//     return responseBody['value']; // List of reports in the group
//   } else {
//     throw Exception('Failed to retrieve reports: ${response.statusCode}');
//   }
// }

// // Function to upload an Excel file to Power BI
// Future<void> uploadExcelFile(String accessToken, String filePath, String groupId) async {
//   final uri =
//       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/imports');
//   final request = http.MultipartRequest('POST', uri)
//     ..headers['Authorization'] = 'Bearer $accessToken'
//     ..files.add(await http.MultipartFile.fromPath('file', filePath));

//   final response = await request.send();

//   if (response.statusCode == 200) {
//     print('Excel file uploaded successfully');
//   } else {
//     final responseBody = await response.stream.bytesToString();
//     print('Failed to upload Excel file. Response: $responseBody');
//     throw Exception('Failed to upload Excel file: ${response.statusCode}');
//   }
// }

// // Function to create a dataset in Power BI
// Future<void> createDataset(String accessToken, String datasetName, String groupId) async {
//   final uri =
//       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/datasets');
//   final response = await http.post(
//     uri,
//     headers: {
//       'Authorization': 'Bearer $accessToken',
//       'Content-Type': 'application/json',
//     },
//     body: json.encode({
//       'name': datasetName,
//     }),
//   );

//   if (response.statusCode == 201) {
//     print('Dataset created successfully');
//   } else {
//     throw Exception('Failed to create dataset: ${response.statusCode}');
//   }
// }

// // Function to create a report in Power BI
// Future<void> createReport(String accessToken, String datasetId, String groupId) async {
//   final uri =
//       Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/reports');
//   final response = await http.post(
//     uri,
//     headers: {
//       'Authorization': 'Bearer $accessToken',
//       'Content-Type': 'application/json',
//     },
//     body: json.encode({
//       'datasetId': datasetId,
//       'name': 'Sample Report',
//     }),
//   );

//   if (response.statusCode == 201) {
//     print('Report created successfully');
//   } else {
//     throw Exception('Failed to create report: ${response.statusCode}');
//   }
// }

// // PowerBIReportPage widget to display the report
// class PowerBIReportPage extends StatelessWidget {
//   final String embedUrl;
//   final String embedToken;
//   final String reportId;
//   final String groupId;

//   const PowerBIReportPage({
//     super.key,
//     required this.embedUrl,
//     required this.embedToken,
//     required this.reportId,
//     required this.groupId,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: Text('Power BI Report')),
//       body: Center(
//         child: ElevatedButton(
//           onPressed: () async {
//             await launchPowerBIReport(embedUrl, embedToken, reportId, groupId);
//           },
//           child: Text('Open Power BI Report'),
//         ),
//       ),
//     );
//   }
// }

// // Main function to tie everything together
// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();

//   // Authenticate and get the access token
//   final accessToken = await authenticate();

//   // Pick an Excel file using file_picker
//   FilePickerResult? result = await FilePicker.platform
//       .pickFiles(type: FileType.custom, allowedExtensions: ['xlsx']);
//   if (result == null) {
//     print("No file selected.");
//     return;
//   }

//   // Get the selected file path
//   final filePath = result.files.single.path!;

//   // Fetch available groups
//   List<dynamic> groups = await getAvailableGroups(accessToken);
//   String groupId = groups[0]['id']; // Use the first group for example

//   // Upload the Excel file
//   await uploadExcelFile(accessToken, filePath, groupId);

//   // Fetch reports in the selected group
//   List<dynamic> reports = await getReportsInGroup(accessToken, groupId);
//   String reportId = reports[0]['id']; // Use the first report for example

//   // Create dataset and report
//   String datasetName = 'Sample Dataset';
//   await createDataset(accessToken, datasetName, groupId);
//   String datasetId = 'your-dataset-id'; // Replace with actual datasetId after creation
//   await createReport(accessToken, datasetId, groupId);

//   // Get the embed token for the report
//   final embedToken = await getEmbedToken(accessToken, reportId);

//   // Embed the report in the app
//   runApp(MyApp(accessToken, filePath, groupId, reportId, embedToken));
// }

// // Main app to prompt user input
// class MyApp extends StatelessWidget {
//   final String accessToken;
//   final String filePath;
//   final String groupId;
//   final String reportId;
//   final String embedToken;

//   const MyApp(this.accessToken, this.filePath, this.groupId, this.reportId, this.embedToken, {super.key});

//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       home: PowerBIReportPage(
//         embedUrl: 'https://app.powerbi.com/reportEmbed', // Provide embed URL here
//         embedToken: embedToken,
//         reportId: reportId,
//         groupId: groupId,
//       ),
//     );
//   }
// }

import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

const String tenantId = "a7d1894d-7354-4c76-9ca8-fe9a3d434e6e";
const String clientId = "88be577d-070e-41e7-8dc9-9ab39ae1d3fa";
const String clientSecret = "****************************************";

// Function to authenticate and get access token
Future<String> authenticate() async {
  final uri = Uri.parse(
      'https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token');
  final response = await http.post(
    uri,
    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
    body: {
      'client_id': clientId,
      'client_secret': clientSecret,
      'scope': 'https://graph.microsoft.com/.default',
      'grant_type': 'client_credentials',
    },
  );

  if (response.statusCode == 200) {
    final responseBody = json.decode(response.body);
    return responseBody['access_token'];
  } else {
    throw Exception('Failed to authenticate: ${response.statusCode}');
  }
}

// Function to get available groups (workspaces)
// Future<List<dynamic>> getAvailableGroups(String accessToken) async {
//   final uri = Uri.parse('https://graph.microsoft.com/v1.0/me/joinedGroups');
//   final response = await http.get(
//     uri,
//     headers: {'Authorization': 'Bearer $accessToken'},
//   );

//   if (response.statusCode == 200) {
//     final responseBody = json.decode(response.body);
//     return responseBody['value']; // List of groups
//   } else {
//     throw Exception('Failed to retrieve groups: ${response.statusCode}');
//   }
// }

// Function to get available reports in a group
Future<List<dynamic>> getReportsInGroup(
    String accessToken, String groupId) async {
  final uri =
      Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/reports');
  final response = await http.get(
    uri,
    headers: {'Authorization': 'Bearer $accessToken'},
  );

  if (response.statusCode == 200) {
    final responseBody = json.decode(response.body);
    return responseBody['value']; // List of reports in the group
  } else {
    throw Exception('Failed to retrieve reports: ${response.statusCode}');
  }
}

// Function to upload an Excel file to Power BI
Future<void> uploadExcelFile(
    String accessToken, String filePath, String groupId) async {
  final uri =
      Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/imports');
  final request = http.MultipartRequest('POST', uri)
    ..headers['Authorization'] = 'Bearer $accessToken'
    ..files.add(await http.MultipartFile.fromPath('file', filePath));

  final response = await request.send();

  if (response.statusCode == 200) {
    if (kDebugMode) {
      print('Excel file uploaded successfully');
    }
  } else {
    final responseBody = await response.stream.bytesToString();
    if (kDebugMode) {
      print('Failed to upload Excel file. Response: $responseBody');
    }
    throw Exception('Failed to upload Excel file: ${response.statusCode}');
  }
}

// Function to create a dataset in Power BI
Future<void> createDataset(
    String accessToken, String datasetName, String groupId) async {
  final uri =
      Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/datasets');
  final response = await http.post(
    uri,
    headers: {
      'Authorization': 'Bearer $accessToken',
      'Content-Type': 'application/json',
    },
    body: json.encode({
      'name': datasetName,
    }),
  );

  if (response.statusCode == 201) {
    if (kDebugMode) {
      print('Dataset created successfully');
    }
  } else {
    throw Exception('Failed to create dataset: ${response.statusCode}');
  }
}

// Function to create a report in Power BI
Future<void> createReport(
    String accessToken, String datasetId, String groupId) async {
  final uri =
      Uri.parse('https://api.powerbi.com/v1.0/myorg/groups/$groupId/reports');
  final response = await http.post(
    uri,
    headers: {
      'Authorization': 'Bearer $accessToken',
      'Content-Type': 'application/json',
    },
    body: json.encode({
      'datasetId': datasetId,
      'name': 'Sample Report',
    }),
  );

  if (response.statusCode == 201) {
    if (kDebugMode) {
      print('Report created successfully');
    }
  } else {
    throw Exception('Failed to create report: ${response.statusCode}');
  }
}

// Future<List<dynamic>> getAvailableGroups(String accessToken) async {
//   final uri = Uri.parse('https://graph.microsoft.com/v1.0/me/joinedGroups');
//   final response = await http.get(
//     uri,
//     headers: {'Authorization': 'Bearer $accessToken'},
//   );

//   if (response.statusCode == 200) {
//     final responseBody = json.decode(response.body);
//     return responseBody['value']; // List of groups
//   } else {
//     print('Failed to retrieve groups: ${response.statusCode}');
//     print('Response body: ${response.body}');
//     throw Exception('Failed to retrieve groups: ${response.statusCode}');
//   }
// }
// // Function to get the embed token for the report

// Function to get available groups (workspaces)
Future<List<dynamic>> getAvailableGroups(String accessToken) async {
  final uri = Uri.parse('https://graph.microsoft.com/v1.0/groups');
  final response = await http.get(
    uri,
    headers: {'Authorization': 'Bearer $accessToken'},
  );
  if (kDebugMode) {
    print('Response: ${response.body}');
  }
  if (response.statusCode == 200) {
    final responseBody = json.decode(response.body);
    return responseBody['value']; // List of groups
  } else {
    if (kDebugMode) {
      print('Failed to retrieve groups: ${response.statusCode}');
      print('Response body: ${response.body}');
    }
    throw Exception('Failed to retrieve groups: ${response.statusCode}');
  }
}

Future<String> getEmbedToken(String accessToken, String reportId) async {
  final uri = Uri.parse(
      'https://api.powerbi.com/v1.0/myorg/reports/$reportId/GenerateToken');
  final response = await http.post(
    uri,
    headers: {
      'Authorization': 'Bearer $accessToken',
      'Content-Type': 'application/json',
    },
    body: json.encode({
      'accessLevel': 'view', // Options: 'view', 'edit'
    }),
  );

  if (response.statusCode == 200) {
    final embedToken = json.decode(response.body)['token'];
    if (kDebugMode) {
      print('Embed token: $embedToken');
    }
    return embedToken;
  } else {
    throw Exception('Failed to get embed token: ${response.statusCode}');
  }
}

// Function to launch the Power BI report in the browser
Future<void> launchPowerBIReport(
    String embedUrl, String embedToken, String reportId, String groupId) async {
  final url =
      '$embedUrl?reportId=$reportId&groupId=$groupId&access_token=$embedToken';
  if (await canLaunch(url)) {
    await launch(url);
  } else {
    throw 'Could not launch $url';
  }
}

// PowerBIReportPage widget to display the report
class PowerBIReportPage extends StatelessWidget {
  final String embedUrl;
  final String embedToken;
  final String reportId;
  final String groupId;

  const PowerBIReportPage({
    super.key,
    required this.embedUrl,
    required this.embedToken,
    required this.reportId,
    required this.groupId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Power BI Report')),
      body: Center(
        child: ElevatedButton(
          onPressed: () async {
            // Launch the Power BI report
            await launchPowerBIReport(embedUrl, embedToken, reportId, groupId);
          },
          child: Text('Open Power BI Report'),
        ),
      ),
    );
  }
}

// Main function to tie everything together
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Authenticate and get the access token
  final accessToken = await authenticate();

  // Pick an Excel file using file_picker
  FilePickerResult? result = await FilePicker.platform
      .pickFiles(type: FileType.custom, allowedExtensions: ['xlsx']);
  if (result == null) {
    if (kDebugMode) {
      print("No file selected.");
    }
    return;
  }

  // Get the selected file path
  final filePath = result.files.single.path!;

  // Fetch available groups
  List<dynamic> groups = await getAvailableGroups(accessToken);
  String groupId = groups[0]['id']; // Use the first group for example

  // Upload the Excel file
  await uploadExcelFile(accessToken, filePath, groupId);

  // Fetch reports in the selected group
  List<dynamic> reports = await getReportsInGroup(accessToken, groupId);
  String reportId = reports[0]['id']; // Use the first report for example

  // Create dataset and report
  String datasetName = 'Sample Dataset';
  await createDataset(accessToken, datasetName, groupId);
  String datasetId =
      'your-dataset-id'; // Replace with actual datasetId after creation
  await createReport(accessToken, datasetId, groupId);

  // Get the embed token for the report
  final embedToken = await getEmbedToken(accessToken, reportId);

  // Embed the report in the app
  runApp(MyApp(accessToken, filePath, groupId, reportId, embedToken));
}

// Main app to prompt user input
class MyApp extends StatelessWidget {
  final String accessToken;
  final String filePath;
  final String groupId;
  final String reportId;
  final String embedToken;

  const MyApp(this.accessToken, this.filePath, this.groupId, this.reportId,
      this.embedToken,
      {super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: PowerBIReportPage(
        embedUrl:
            'https://app.powerbi.com/reportEmbed', // Provide embed URL here
        embedToken: embedToken,
        reportId: reportId,
        groupId: groupId,
      ),
    );
  }
}
