import 'package:dio/dio.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

class DioHelper {
  static Dio dio = Dio();

  static Future<void> init() {
    BaseOptions baseOptions = BaseOptions(
      baseUrl: EndPoints.baseUrl,
      receiveDataWhenStatusError: true,
      validateStatus: (status) =>
          true, // Accept all responses for manual handling
    );

    dio = Dio(baseOptions);
    addDioInterceptor();
    return Future.value();
  }

  Future<Response?> get({required String endPoint, data}) async {
    dio.options.headers = {
      "Accept": "application/json",
      "Lang": CacheHelper.getCurrentLanguage().toString(),
      "Authorization":
          "Bearer ${await CacheHelper.getSecuredString(key: CacheKeys.userToken)}",
    };
    return await dio.get(endPoint, queryParameters: data);
  }

  Future<Response?> post({required String endPoint, data}) async {
    dio.options.headers = {
      "Accept": "application/json",
      "Lang": CacheHelper.getCurrentLanguage().toString(),
      "Authorization":
          "Bearer ${await CacheHelper.getSecuredString(key: CacheKeys.userToken)}",
    };
    return await dio.post(endPoint, data: data);
  }

  static void addDioInterceptor() {
    dio.interceptors.addAll([
      PrettyDioLogger(
        requestBody: true,
        requestHeader: true,
        responseHeader: true,
        responseBody: true,
      ),
      InterceptorsWrapper(
        onError: (DioException error, ErrorInterceptorHandler handler) async {
          final response = error.response;

          if (response?.statusCode == 500) {
            // Log custom Crashlytics data
            FirebaseCrashlytics.instance.setCustomKey(
                'url', response?.requestOptions.uri.toString() ?? 'unknown');
            FirebaseCrashlytics.instance.setCustomKey(
                'method', response?.requestOptions.method ?? 'unknown');
            FirebaseCrashlytics.instance
                .setCustomKey('status_code', response?.statusCode ?? 0);
            FirebaseCrashlytics.instance.setCustomKey(
                'response_data', response?.data.toString() ?? 'no response');

            await FirebaseCrashlytics.instance.recordError(
              error,
              error.stackTrace,
              reason: 'HTTP 500 Internal Server Error',
              fatal: false,
            );
          }

          return handler.next(error); // Continue to pass the error
        },
      ),
    ]);
  }
}
