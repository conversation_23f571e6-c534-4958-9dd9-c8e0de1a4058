import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

final ThemeData lightTheme = ThemeData(
    appBarTheme: AppBarTheme(
      centerTitle: true,
      color: AppColors.scaffoldBackground,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      scrolledUnderElevation: 0,
      titleTextStyle: Styles.contentEmphasis
          .copyWith(fontSize: 20.sp, fontWeight: FontWeight.w500),
      iconTheme: IconThemeData(color: AppColors.neutralColor1600, size: 20.sp),
      actionsIconTheme:
          IconThemeData(color: AppColors.neutralColor1600, size: 20.sp),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: AppColors.primaryColor800,
      // unselectedItemColor: AppColors.,
    ),
    scaffoldBackgroundColor: AppColors.scaffoldBackground,
    primaryColor: AppColors.primaryColor800,
    colorScheme: ColorScheme.light(
      primary: AppColors.primaryColor800,
    ));
