import 'package:flutter/material.dart';

class AppColors {
  static const primaryColor1000 = Color(0xff004023);

  static const primaryColor900 = Color(0xff13476D);
  static const primaryColor800 = Color(0xff13476D);

  static const primaryColor100 = Color(0xffB7D9F2);
  static const primaryColor10 = Color(0x1A006638);


  static const neutralColor1600 = Color(0xff373737);
  static const neutralColor1400 = Color(0xff151515);
  static const neutralColor1300 = Color(0xff272727);
  static const neutralColor1200 = Color(0xff383838);
  static const neutralColor1100 = Color(0xff4A4A4A);
  static const neutralColor1000 = Color(0xff5C5C5C);
  static const neutralColor900 = Color(0xff6E6E6E);
  static const neutralColor800 = Color(0xff808080);
  static const neutralColor700 = Color(0xff919191);
  static const neutralColor600 = Color(0xffBABABA);
  static const neutralColor500 = Color(0xffB5B5B5);
  static const neutralColor400 = Color(0xffC7C7C7);
  static const neutralColor300 = Color(0xffD8D8D8);
  static const neutralColor200 = Color(0xffEFEFEF);
  static const neutralColor100 = Color(0xffFCFCFC);
  static Color neutralColor10 = Color(0xff030303).withValues(alpha: 0.1);

  static const redColor100 = Color(0xffFB3748);
  static const redColor200 = Color(0xffD00416);
  static const redColor10 = Color(0x1AFB3748);

  static const yellowColor100 = Color(0xffFFDB43);
  static const yellowColor200 = Color(0xffDFB400);
  static const yellowColor10 = Color(0x1AFFDB43);

  static const greenColor100 = Color(0xff84EBB4);
  static const greenColor200 = Color(0xff1FC16B);
  static const greenColor10 = Color(0x1A1FC16B);

  static const dividerColor = Color(0xff92929D);
  static const scaffoldBackground = Color(0xffFCFCFC);
}
