import 'dart:ui';

import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/services/di/dependency_injection.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/check%20in%20&%20checkOut/data/repo/check_in_repo.dart';

final repo = getIt<CheckInRepo>();

Future<void> checkIn({
  required String checkInTime,
  VoidCallback? onSuccess,
  VoidCallback? onFailure,
}) async {
  showLoading();

  final result = await getIt<CheckInRepo>().checkIn(
    checkInTime: checkInTime,
    latitude: double.parse(CacheHelper.getData(key: CacheKeys.userLatitude)),
    longitude: double.parse(CacheHelper.getData(key: CacheKeys.userLongitude)),
  );

  hideLoading();

  result.when(
    success: (_) => onSuccess?.call(),
    failure: (_) => onFailure?.call(),
  );
}

Future<void> checkOut({
  required String checkOutTime,
  VoidCallback? onSuccess,
  VoidCallback? onFailure,
}) async {
  showLoading();

  final result = await getIt<CheckInRepo>().checkOut(
    checkOutTime: checkOutTime,
    latitude: double.parse(CacheHelper.getData(key: CacheKeys.userLatitude)),
    longitude: double.parse(CacheHelper.getData(key: CacheKeys.userLongitude)),
  );

  hideLoading();

  result.when(
    success: (_) => onSuccess?.call(),
    failure: (_) => onFailure?.call(),
  );
}
