import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/error_handler.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/features/check%20in%20&%20checkOut/data/api%20services/check_in_api_services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class CheckInRepo {
  final CheckInApiServices authApiServices;

  CheckInRepo(this.authApiServices);

  /// Check in
  Future<ApiResult<String>> checkIn({
    required String checkInTime,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await authApiServices.checkIn(
        checkInTime: checkInTime,
        latitude: latitude,
        longitude: longitude,
      );

      if (response == null) {
        return ApiResult.failure(ErrorHandler.handleApiError(null));
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        /// Show success toast
        ToastManager.showCustomToast(
          message: 'home.clockingSuccessfully'.tr(),
          backgroundColor: AppColors.greenColor200,
          icon: Icons.check_circle_outline,
          duration: const Duration(seconds: 3),
        );
        return ApiResult.success('Check In Successfully');
      } else {
        return ApiResult.failure(ErrorHandler.handleApiError(response));
      }
    } on DioException catch (e) {
      return ApiResult.failure(ErrorHandler.handleDioError(e));
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Unexpected error: $e');
        print('📌 Stack trace: $stackTrace');
      }
      return ApiResult.failure(ErrorHandler.handleUnexpectedError(e));
    }
  }

  /// Check out
  Future<ApiResult<String>> checkOut({
    required String checkOutTime,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await authApiServices.checkOut(
        checkOutTime: checkOutTime,
        latitude: latitude,
        longitude: longitude,
      );

      if (response == null) {
        return ApiResult.failure(ErrorHandler.handleApiError(null));
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        /// Show success toast
        ToastManager.showCustomToast(
          message: 'home.clockingoutSuccessfully'.tr(),
          backgroundColor: AppColors.greenColor200,
          icon: Icons.check_circle_outline,
          duration: const Duration(seconds: 3),
        );
        return ApiResult.success('Check Out Successfully');
      } else {
        return ApiResult.failure(ErrorHandler.handleApiError(response));
      }
    } on DioException catch (e) {
      return ApiResult.failure(ErrorHandler.handleDioError(e));
    } catch (e, _) {
      return ApiResult.failure(ErrorHandler.handleUnexpectedError(e));
    }
  }
}
