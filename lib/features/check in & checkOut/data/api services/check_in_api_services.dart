import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class CheckInApiServices {
  CheckInApiServices(this._dioFactory);

  final Dio<PERSON>elper _dioFactory;

  Future<Response?> checkIn({
    required String checkInTime,
    required double latitude,
    required double longitude,
  }) async =>
      await _dioFactory.post(
        endPoint: EndPoints.checkInEP,
        data: {
          "check_in_at": checkInTime,
          "check_in_latitude": latitude,
          "check_in_longitude": longitude
        },
      );

  Future<Response?> checkOut({
    required String checkOutTime,
    required double latitude,
    required double longitude,
  }) async =>
      await _dioFactory.post(
        endPoint: EndPoints.checkOutEP,
        data: {
          "check_out_at": checkOutTime,
          "check_out_latitude": latitude,
          "check_out_longitude": longitude,
        },
      );
}
