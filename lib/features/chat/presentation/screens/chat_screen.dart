import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/appbar_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/core/widgets/loading_widget.dart';
import 'package:erp/features/chat/bloc/cubit/chat_cubit.dart';
import 'package:erp/features/chat/bloc/cubit/chat_state.dart';
import 'package:erp/features/chat/presentation/widgets/chat_list_skeletonizer_widget.dart';
import 'package:erp/features/chat/presentation/widgets/chat_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatScreen extends StatelessWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final singleChatCubit = BlocProvider.of<ChatCubit>(context);

    return BlocBuilder<ChatCubit, ChatState>(
      buildWhen: (previous, current) =>
          current is GetSpecificTicketError ||
          current is GetSpecificTicketLoading ||
          current is GetSpecificTicketSuccess,
      builder: (context, state) {
        if (state is GetSingleChatLoadingState) {
          return const LoadingWidget();
        } else {
          return Scaffold(
            backgroundColor: AppColors.scaffoldBackground,
            body: Column(
              children: [
                AppBarWidget(
                  rowWidget: Row(
                    spacing: 16.w,
                    children: [
                      BackButton(),
                      singleChatCubit.ticketDetailsModel == null
                          ? Text(
                              '#TICKET-....',
                              style: Styles.heading2.copyWith(
                                color: AppColors.scaffoldBackground,
                              ),
                            )
                          : Text(
                              '#TICKET-${singleChatCubit.ticketDetailsModel!.data.id}',
                              style: Styles.heading2.copyWith(
                                color: AppColors.scaffoldBackground,
                              ),
                            ),
                    ],
                  ),
                ),
                Expanded(
                  child: singleChatCubit.ticketDetailsModel == null ||
                          state is GetSpecificTicketLoading
                      ? ChatListSkeletonizerWidget()
                      : singleChatCubit
                              .ticketDetailsModel!.data.messages.isEmpty
                          ? Center(
                              child: Text(
                                'support.noMessages'.tr(),
                                style: Styles.heading2.copyWith(
                                  color: AppColors.scaffoldBackground,
                                ),
                              ),
                            )
                          : ChatListWidget(singleChatCubit: singleChatCubit),
                ),
              ],
            ),
            bottomNavigationBar: Container(
              padding: EdgeInsets.only(
                  left: 20.sp,
                  top: 10.sp,
                  right: 20.sp,
                  bottom: MediaQuery.of(context).viewInsets.bottom + 10),
              child: Form(
                key: singleChatCubit.formKey,
                child: Row(
                  children: [
                    Expanded(
                      child: CustomTextFormFieldWidget(
                        backgroundColor: Colors.white,
                        isChat: true,
                        validator: (value) {
                          if (value!.trim().isEmpty) {
                            return "please_enter_valid_message".tr();
                          }
                          return null;
                        },
                        onChanged: (value) {
                          singleChatCubit.showSendBUtton(letters: value);
                        },
                        controller: singleChatCubit.masseageController,
                        labelText: "message".tr(),
                      ),
                    ),
                    BlocBuilder<ChatCubit, ChatState>(
                      buildWhen: (previous, current) =>
                          current is IsTypeingState,
                      builder: (context, state) {
                        return Visibility(
                          visible: singleChatCubit.isTypeing,
                          child: IconButton(
                              onPressed: () {
                                if (singleChatCubit.formKey.currentState!
                                    .validate()) {
                                  singleChatCubit.sendMessage(
                                    ticketId: singleChatCubit
                                        .ticketDetailsModel!.data.id,
                                    message:
                                        singleChatCubit.masseageController.text,
                                  );
                                }
                              },
                              icon: Icon(
                                Icons.send,
                                color: AppColors.primaryColor900,
                              )),
                        );
                      },
                    )
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }
}
