import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/features/chat/bloc/cubit/chat_cubit.dart';
import 'package:erp/features/chat/presentation/widgets/chat_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatListWidget extends StatelessWidget {
  const ChatListWidget({
    super.key,
    required this.singleChatCubit,
  });

  final ChatCubit singleChatCubit;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      padding:
          EdgeInsets.only(top: 20.h, right: 20.w, left: 20.h, bottom: 40.h),
      controller: singleChatCubit.scrollController,
      // Keep controller
      physics: const BouncingScrollPhysics(),
      itemBuilder: (BuildContext context, int index) {
        final message =
            singleChatCubit.ticketDetailsModel!.data.messages[index];

        return ChatTextItemWidget(
          isSender: message.isAdmin == 1 ? false : true,
          message: message.message,
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return Column(
          children: [
            16.verticalSpace,
            Container(
              height: 1.sp,
              color: AppColors.dividerColor,
            ),
            16.verticalSpace,
          ],
        );
      },
      itemCount: singleChatCubit.ticketDetailsModel!.data.messages.length,
    );
  }
}
