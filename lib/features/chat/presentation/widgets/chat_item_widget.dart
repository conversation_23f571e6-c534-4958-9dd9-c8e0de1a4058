import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ChatTextItemWidget extends StatelessWidget {
  const ChatTextItemWidget(
      {super.key, required this.isSender, required this.message});

  final bool isSender;
  final String message;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: isSender ? Alignment.centerRight : Alignment.centerLeft,
      child: Row(
        mainAxisAlignment:
            isSender ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isSender) ...[
            Image.asset(
              Assets.assetsImagesPngsEditProfileImage,
              width: 40.w,
              height: 40.h,
            ),
            SizedBox(width: 8.w),
          ],
          Expanded(
            child: SelectableText(
              message,
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor1200,
              ),
              textAlign: isSender ? TextAlign.right : TextAlign.left,
              // textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
            ),
          ),
          if (isSender) ...[
            SizedBox(width: 8.w),
            CircleAvatar(
              radius: 20.r,
              backgroundColor: AppColors.neutralColor100,
              child: Image.asset(
                "assets/images/pngs/erp.png",
                fit: BoxFit.scaleDown,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
