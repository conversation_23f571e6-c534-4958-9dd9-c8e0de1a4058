import 'package:json_annotation/json_annotation.dart';

part 'ticket_details_model.g.dart';

@JsonSerializable()
class TicketDetailsModel {
  final String status;
  final String error;
  final int code;
  final TicketData data;

  TicketDetailsModel({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory TicketDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$TicketDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$TicketDetailsModelToJson(this);
}

@JsonSerializable()
class TicketData {
  final int id;
  final String title;
  final String client;
  final String email;
  final String image;
  final String status;
  final List<TicketMessage> messages;

  TicketData({
    required this.id,
    required this.title,
    required this.client,
    required this.email,
    required this.image,
    required this.status,
    required this.messages,
  });

  factory TicketData.fromJson(Map<String, dynamic> json) =>
      _$TicketDataFromJson(json);

  Map<String, dynamic> toJson() => _$TicketDataToJson(this);
}

@JsonSerializable()
class TicketMessage {
  final String message;
  @JsonKey(name: 'is_admin')
  final int isAdmin;
  final String? employee;

  TicketMessage({
    required this.message,
    required this.isAdmin,
    this.employee,
  });

  factory TicketMessage.fromJson(Map<String, dynamic> json) =>
      _$TicketMessageFromJson(json);

  Map<String, dynamic> toJson() => _$TicketMessageToJson(this);
}
