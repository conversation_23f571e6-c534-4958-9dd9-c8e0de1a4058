import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/chat/data/api_services/api_services.dart';
import 'package:erp/features/chat/data/model/ticket_details_data_model/ticket_details_model.dart';

class ChatRepo {
  final ChatApiServices ticketApiServices;

  ChatRepo(this.ticketApiServices);

  /// Get Specific Ticket
  Future<ApiResult<TicketDetailsModel>> getSpecificTicket(int ticketId) async {
    final response = await ticketApiServices.getSpecificTicket(ticketId);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        TicketDetailsModel ticketDetailsModel =
            TicketDetailsModel.fromJson(response.data);
        return ApiResult.success(ticketDetailsModel);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Send Message
  Future<ApiResult<String>> sendMessage(int ticketId, String message) async {
    final response = await ticketApiServices.sendMessage(ticketId, message);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Message Sent Successfully');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
