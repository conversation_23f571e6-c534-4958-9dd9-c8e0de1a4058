import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/profile/bloc/cubit/profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ProfileCubit>();
    final profile = cubit.profileModel!;

    return Scaffold(
      appBar: AppBar(
        title: Text('profile.profile'.tr()),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 20.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // const ProfileImageWidget(),
            ProfileFieldWidget(
                label: 'profile.jobTitle'.tr(),
                value: profile.data.position.name.en),
            // ProfileFieldWidget(
            //     label: "Date of Birth", value: profile.data. ?? "N/A"),
            // ProfileFieldWidget(
            //     label: "Nationality ID",
            //     value: profile.data.?.toString() ?? "N/A"),
            ProfileFieldWidget(
                label: 'profile.phonenumber'.tr(), value: profile.data.phone),
            ProfileFieldWidget(
                label: 'profile.email'.tr(), value: profile.data.email),
            ProfileFieldWidget(
                label: 'profile.baseSalary'.tr(),
                value: profile.data.salary.toString()),
            ProfileFieldWidget(
                label: 'profile.city'.tr(), value: profile.data.branch.city.en),
            ProfileFieldWidget(
                label: 'profile.address'.tr(),
                value: profile.data.branch.name.en),
            ProfileFieldWidget(
                label: 'profile.employmenthistory'.tr(),
                value: profile.data.contractStartDate),
            ProfileFieldWidget(
                label: 'profile.contracttype'.tr(),
                value: profile.data.contractType),
            if (profile.data.shift != null)
              ProfileFieldWidget(
                label: 'profile.workinghours'.tr(),
                value:
                    "${profile.data.shift!.timeFrom} - ${profile.data.shift!.timeTo}",
              ),
          ],
        ),
      ),
    );
  }
}

class ProfileFieldWidget extends StatelessWidget {
  final String label;
  final String value;

  const ProfileFieldWidget({
    super.key,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: Styles.contentEmphasis),
          CustomTextFormFieldWidget(
            hintText: value,
            readOnly: true,
          ),
        ],
      ),
    );
  }
}
