import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/profile/bloc/cubit/profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProfileImageWidget extends StatelessWidget {
  const ProfileImageWidget({super.key});

  @override
  Widget build(BuildContext context) {
    ProfileCubit profileCubit = BlocProvider.of<ProfileCubit>(context);
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) => current is PickImageSuccessState,
      builder: (context, state) {
        return Center(
          child: Column(
            children: [
              Stack(
                alignment: Alignment.bottomRight,
                children: [
                  Container(
                    clipBehavior: Clip.antiAliasWithSaveLayer,
                    height: 80.sp,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          width: 1.sp,
                        )),
                    child: profileCubit.pickedImage != null
                        ? Image.file(
                            profileCubit.pickedImage!,
                            height: 250.sp,
                            fit: BoxFit.fill,
                          )
                        : CachedNetworkImage(
                            placeholder: (context, url) => Skeletonizer(
                              enabled: true,
                              child: Container(
                                height: 250.sp,
                                width: 250.sp,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: AppColors.neutralColor300,
                                ),
                              ),
                            ),
                            imageUrl:
                                // context.read<ProfileCubit>().profileModel!.imag,
                                "",
                            height: 250.sp,
                            fit: BoxFit.fill,
                          ),
                  ),
                  InkWell(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: Text("profile.changeimage".tr()),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                TextButton(
                                    onPressed: () {
                                      profileCubit.pickImage(
                                          ImageSource.gallery, context);
                                      Navigator.pop(context);
                                    },
                                    child: Text("profile.pickfromdevice".tr())),
                                TextButton(
                                    onPressed: () {
                                      profileCubit.pickImage(
                                          ImageSource.camera, context);
                                      Navigator.pop(context);
                                    },
                                    child: Text("profile.pickfromCamera".tr())),
                              ],
                            ),
                          );
                        },
                      );
                    },
                    child: Container(
                      clipBehavior: Clip.antiAliasWithSaveLayer,
                      padding: EdgeInsets.all(4.sp),
                      width: 24.sp,
                      height: 24.sp,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primaryColor900),
                      child:
                          SvgPicture.asset(Assets.assetsImagesSvgsCameraIcon),
                    ),
                  ),
                ],
              ),
              10.verticalSpace,
              Text(context.read<ProfileCubit>().profileModel!.data.name,
                  style: Styles.featureEmphasis),
            ],
          ),
        );
      },
    );
  }
}
