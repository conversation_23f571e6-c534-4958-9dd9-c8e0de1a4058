import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class ProfileApiServices {
  ProfileApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Update password
  Future<Response?> updatePassword({
    required String oldPassword,
    required String newPassword,
    required String newRePassword,
  }) async {
    Map<String, dynamic> formDataMap = {
      'oldPassword': oldPassword,
      'newPassword': newPassword,
      'newRePassword': newRePassword,
    };
    FormData formData = FormData.fromMap(formDataMap);
    return _dioFactory.post(endPoint: EndPoints.updatePassword, data: formData);
  }

  Future<Response?> getProfile() async {
    return _dioFactory.get(
      endPoint: EndPoints.getProfile,
    );
  }
}
