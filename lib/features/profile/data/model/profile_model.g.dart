// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProfileResponse _$ProfileResponseFromJson(Map<String, dynamic> json) =>
    ProfileResponse(
      data: Profile.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProfileResponseToJson(ProfileResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

Profile _$ProfileFromJson(Map<String, dynamic> json) => Profile(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      branch: Branch.fromJson(json['branch'] as Map<String, dynamic>),
      department:
          Department.fromJson(json['department'] as Map<String, dynamic>),
      position: Position.fromJson(json['position'] as Map<String, dynamic>),
      shift: json['shift'] == null
          ? null
          : Shift.fromJson(json['shift'] as Map<String, dynamic>),
      salary: (json['salary'] as num).toInt(),
      status: json['status'] as String,
      contractType: json['contract_type'] as String,
      maritalStatus: json['marital_status'] as String,
      annualLeaveDays: (json['annual_leave_days'] as num).toInt(),
      normalDays: (json['normal_days'] as num).toInt(),
      sickDays: (json['sick_days'] as num).toInt(),
      availableAnnualLeaveDays:
          (json['available_annual_leave_days'] as num).toInt(),
      availableNormalDays: (json['available_normal_days'] as num).toInt(),
      availableSickDays: (json['available_sick_days'] as num).toInt(),
      contractStartDate: json['contract_start_date'] as String,
      contractEndDate: json['contract_end_date'] as String,
      accessToken: json['access_token'] as String?,
      role: json['role'] as String,
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$ProfileToJson(Profile instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'branch': instance.branch,
      'department': instance.department,
      'position': instance.position,
      'shift': instance.shift,
      'salary': instance.salary,
      'status': instance.status,
      'contract_type': instance.contractType,
      'marital_status': instance.maritalStatus,
      'annual_leave_days': instance.annualLeaveDays,
      'normal_days': instance.normalDays,
      'sick_days': instance.sickDays,
      'available_annual_leave_days': instance.availableAnnualLeaveDays,
      'available_normal_days': instance.availableNormalDays,
      'available_sick_days': instance.availableSickDays,
      'contract_start_date': instance.contractStartDate,
      'contract_end_date': instance.contractEndDate,
      'access_token': instance.accessToken,
      'role': instance.role,
      'permissions': instance.permissions,
    };

Branch _$BranchFromJson(Map<String, dynamic> json) => Branch(
      id: (json['id'] as num).toInt(),
      name: Localized.fromJson(json['name'] as Map<String, dynamic>),
      country: Country.fromJson(json['country'] as Map<String, dynamic>),
      city: Localized.fromJson(json['city'] as Map<String, dynamic>),
      annualLeaveDays: (json['annual_leave_days'] as num).toInt(),
      normalDays: (json['normal_days'] as num).toInt(),
      sickDays: (json['sick_days'] as num).toInt(),
    );

Map<String, dynamic> _$BranchToJson(Branch instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'country': instance.country,
      'city': instance.city,
      'annual_leave_days': instance.annualLeaveDays,
      'normal_days': instance.normalDays,
      'sick_days': instance.sickDays,
    };

Country _$CountryFromJson(Map<String, dynamic> json) => Country(
      id: (json['id'] as num).toInt(),
      name: Localized.fromJson(json['name'] as Map<String, dynamic>),
      code: json['code'] as String,
    );

Map<String, dynamic> _$CountryToJson(Country instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
    };

Department _$DepartmentFromJson(Map<String, dynamic> json) => Department(
      id: (json['id'] as num).toInt(),
      name: Localized.fromJson(json['name'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DepartmentToJson(Department instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };

Position _$PositionFromJson(Map<String, dynamic> json) => Position(
      id: (json['id'] as num).toInt(),
      name: Localized.fromJson(json['name'] as Map<String, dynamic>),
      mangers: json['mangers'] == null
          ? null
          : Manager.fromJson(json['mangers'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PositionToJson(Position instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'mangers': instance.mangers,
    };

Manager _$ManagerFromJson(Map<String, dynamic> json) => Manager(
      id: (json['id'] as num).toInt(),
      name: Localized.fromJson(json['name'] as Map<String, dynamic>),
      mangers: json['mangers'] == null
          ? null
          : Manager.fromJson(json['mangers'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ManagerToJson(Manager instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'mangers': instance.mangers,
    };

Shift _$ShiftFromJson(Map<String, dynamic> json) => Shift(
      id: (json['id'] as num).toInt(),
      title: const TitleConverter().fromJson(json['title']),
      dateFrom: json['date_from'] as String,
      dateTo: json['date_to'] as String,
      timeFrom: json['time_from'] as String,
      timeTo: json['time_to'] as String,
    );

Map<String, dynamic> _$ShiftToJson(Shift instance) => <String, dynamic>{
      'id': instance.id,
      'title': const TitleConverter().toJson(instance.title),
      'date_from': instance.dateFrom,
      'date_to': instance.dateTo,
      'time_from': instance.timeFrom,
      'time_to': instance.timeTo,
    };

Localized _$LocalizedFromJson(Map<String, dynamic> json) => Localized(
      en: json['en'] as String,
      ar: json['ar'] as String,
    );

Map<String, dynamic> _$LocalizedToJson(Localized instance) => <String, dynamic>{
      'en': instance.en,
      'ar': instance.ar,
    };
