import 'dart:io';

import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/profile/data/model/profile_model.dart';
import 'package:erp/features/profile/data/repos/profile_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit(this.profileRepository) : super(ProfileInitial());

  File? pickedImage;
  final ProfileRepo profileRepository;

  final TextEditingController confirmNewPassword = TextEditingController();
  final TextEditingController oldPassword = TextEditingController();
  final TextEditingController newPassword = TextEditingController();
  final formKey = GlobalKey<FormState>();

  bool isObscure = true;
  bool isObscure1 = true;
  bool isObscure2 = true;

  void toggleObscure() {
    isObscure = !isObscure;
    emit(TogglePasswordState());
  }

  void toggleObscure1() {
    isObscure1 = !isObscure1;
    emit(TogglePasswordState());
  }

  void toggleObscure2() {
    isObscure2 = !isObscure2;
    emit(TogglePasswordState());
  }

  bool isConfirm = true;

  void changeToggleFunction() {
    isConfirm = !isConfirm;
    emit(IsConfirmGoTo());
  }

  Future pickImage(ImageSource source, context) async {
    try {
      final picker = ImagePicker();
      final image = await picker.pickImage(source: source);
      if (image == null) return;
      final imageTemp = File(image.path);

      pickedImage = imageTemp;

      emit(PickImageSuccessState());
    } catch (e) {
      emit(PickImageErrorState());
    }
  }

  Future updatePassword() async {
    showLoading();
    emit(UpdatePasswordLoadingState());
    final result = await profileRepository.updatePassword(
      oldPassword: oldPassword.text,
      newPassword: newPassword.text,
      newRePassword: confirmNewPassword.text,
    );
    result.when(success: (success) {
      hideLoading();
      emit(UpdatePasswordSuccessState());
    }, failure: (error) {
      hideLoading();
      emit(UpdatePasswordErrorState());
    });
  }

  bool hasMinLength = false;
  bool hasUpperLowerCase = false;
  bool hasNumber = false;
  bool passwordsMatch = false;

  void validatePasswordConditions() {
    final password = newPassword.text;
    final confirmPassword = confirmNewPassword.text;

    hasMinLength = password.length >= 8;
    hasUpperLowerCase = RegExp(r'(?=.*[a-z])(?=.*[A-Z])').hasMatch(password);
    hasNumber = RegExp(r'[0-9]').hasMatch(password);
    passwordsMatch = password == confirmPassword;

    emit(PasswordValidationChanged());
  }

  @override
  Future<void> close() {
    confirmNewPassword.dispose();
    oldPassword.dispose();
    newPassword.dispose();
    return super.close();
  }

  ProfileResponse? profileModel;
  void getProfile() async {
    emit(GetProfileLoadingState());
    final result = await profileRepository.getProfile();
    result.when(success: (data) {
      profileModel = data;
      emit(GetProfileSuccessState());
    }, failure: (error) {
      emit(GetProfileErrorState());
    });
  }
}
