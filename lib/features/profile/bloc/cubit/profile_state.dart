part of 'profile_cubit.dart';

abstract class ProfileState {}

final class ProfileInitial extends ProfileState {}
final class PickImageSuccessState extends ProfileState {}

final class PickImageErrorState extends ProfileState {}

/// update States
final class UpdatePasswordSuccessState extends ProfileState {}

final class UpdatePasswordLoadingState extends ProfileState {}

final class  UpdatePasswordErrorState extends ProfileState {}

/// Toggle  up date Password States
final class TogglePasswordState extends ProfileState {}

final class IsConfirmGoTo extends ProfileState {}

class PasswordValidationChanged extends ProfileState {}



final class GetProfileLoadingState extends ProfileState {}

final class GetProfileSuccessState extends ProfileState {}

final class  GetProfileErrorState extends ProfileState {}