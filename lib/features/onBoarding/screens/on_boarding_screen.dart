import 'package:dots_indicator/dots_indicator.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/onBoarding/Bloc/on_boarding_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../Bloc/on_boarding_states.dart';

class OnBoardingScreen extends StatelessWidget {
  const OnBoardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    OnBoardingCubit onBoardingCubit = BlocProvider.of<OnBoardingCubit>(context);
    return BlocBuilder<OnBoardingCubit, OnBoardingStates>(
      builder: (context, state) {
        return PopScope(
          canPop: false,
          
          child: Scaffold(
            appBar: AppBar(
              title: DotsIndicator(
                dotsCount: 3,
                position: onBoardingCubit.onBoardingIndex,
                decorator: DotsDecorator(
                  activeColor: AppColors.primaryColor800,
                  color: AppColors.primaryColor100,
                  size: Size(30.r, 5.r),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  activeSize: Size(30.r, 5.r),
                  activeShape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
              actions: [
                TextButton(
                    onPressed: () {
                      CacheHelper.saveData(
                          key: CacheKeys.isFirstOpen, value: true);
                      context.pushNamedAndRemoveUntil(Routes.loginScreen);
                    },
                    child: Text('onboarding.skip'.tr(),
                        style: Styles.contentEmphasis.copyWith(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.primaryColor100))),
              ],
            ),
            body: PageView.builder(
              itemCount: onBoardingCubit.onBoardingImageUrls.length,
              controller: onBoardingCubit.pageController,
              onPageChanged: (index) {
                onBoardingCubit.changeOnBoardingIndex(index);
              },
              itemBuilder: (context, index) {
                return Stack(
                  fit: StackFit.expand,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.0.sp),
                      child: Image.asset(
                        onBoardingCubit.onBoardingImageUrls[
                            onBoardingCubit.onBoardingIndex],
                        fit: BoxFit.scaleDown,
                        // height: 250,
                      ),
                    ),
                    // Positioned(
                    //   bottom: 150.sp,
                    //   // textDirection: TextDirection.ltr,
                    //   child: SvgPicture.asset(
                    //     // onBoardingCubit.onBoardingImageUrls[
                    //     //     onBoardingCubit],
                    //     Assets.assetsImagesSvgsOnboarding,
                    //     fit: BoxFit.scaleDown,
                    //     height: 250,
                    //   ),
                    // ),
                    Positioned(
                      bottom: 0.h,
                      left: 0.w,
                      right: 0.w,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 20.sp, horizontal: 16.sp),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(10.sp),
                              topRight: Radius.circular(10.sp),
                            ),
                            color: Colors.white,
                            border:
                                Border.all(color: AppColors.primaryColor100)),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              onBoardingCubit.onBoardingTitles[
                                  onBoardingCubit.onBoardingIndex],
                              style: Styles.captionBold.copyWith(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 10.h),
                            Text(
                              onBoardingCubit.onBoardingDescriptions[
                                  onBoardingCubit.onBoardingIndex],
                              style: Styles.captionBold.copyWith(
                                  fontSize: 14.sp,
                                  color: AppColors.neutralColor600),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 10.h),
                            DotsIndicator(
                              dotsCount: 3,
                              position: onBoardingCubit.onBoardingIndex,
                              decorator: DotsDecorator(
                                activeColor: AppColors.primaryColor800,
                                color: AppColors.primaryColor100,
                                size: Size(30.r, 5.r),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                                activeSize: Size(30.r, 5.r),
                                activeShape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                              ),
                            ),
                            SizedBox(height: 10.h),
                            CustomButtonWidget(
                              text: onBoardingCubit.onBoardingIndex == 2
                                  ? 'onboarding.getStarted'.tr()
                                  : 'onboarding.next'.tr(),
                              onPressed: () async {
                                if (onBoardingCubit.onBoardingIndex == 2) {
                                  await CacheHelper.saveData(
                                      key: CacheKeys.isFirstOpen, value: true);
                                  context.pushNamedAndRemoveUntil(
                                      Routes.loginScreen);
                                } else {
                                  onBoardingCubit.changeOnBoardingIndex(
                                      onBoardingCubit.onBoardingIndex + 1);
                                }
                              },
                            ),
                            SizedBox(height: 20.h),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }
}
