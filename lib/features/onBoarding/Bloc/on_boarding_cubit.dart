import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/features/onBoarding/Bloc/on_boarding_states.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class OnBoardingCubit extends Cubit<OnBoardingStates> {
  OnBoardingCubit() : super(OnBoardingInitialState());

  int onBoardingIndex = 0;
  void changeOnBoardingIndex(int index) {
    onBoardingIndex = index;

    emit(OnBoardingNextState());
  }

  PageController pageController = PageController(initialPage: 0);

  List<String> onBoardingImageUrls = [
    Assets.assetsImagesPngsOnboarding1,
    Assets.assetsImagesPngsOnboarding2,
    Assets.assetsImagesPngsOnboarding3,
  ];
  List<String> onBoardingTitles = [
    'onboarding.easilytrack'.tr(),
    'onboarding.gettheattendance'.tr(),
    'onboarding.followthestatus'.tr(),
  ];

  List<String> onBoardingDescriptions = [
    'onboarding.startnowandwork'.tr(),
    'onboarding.knowthedates'.tr(),
    'onboarding.saveMoney'.tr(),
  ];
}
