import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificationScreen extends StatelessWidget {
  final List<Map<String, dynamic>> notifications = [
    {
      "title": "Payment Successful",
      "subtitle": "The hotel name has been booking was successful",
      "time": "Now",
      "icon": Icons.payment,
      "date": "Today",
    },
    {
      "title": "Reminder",
      "subtitle":
          "There are 3 days left to start your reservation in the hotel name",
      "time": "2 min ago",
      "icon": Icons.alarm,
      "date": "Today",
    },
    {
      "title": "40% Special Discount!",
      "subtitle": "Special promotion only valid today",
      "time": "1 day ago",
      "icon": Icons.card_giftcard,
      "date": "Yesterday",
      "isUnread": true
    },
    {
      "title": "Credit Card Connected!",
      "subtitle": "Credit Card has been linked",
      "time": "1 day ago",
      "icon": Icons.credit_card,
      "date": "Yesterday",
    },
    {
      "title": "40% Special Discount!",
      "subtitle": "Special promotion only valid today",
      "time": "2 Mon ago",
      "icon": Icons.card_giftcard,
      "date": "December 09, 2023",
    },
    {
      "title": "Credit Card Connected!",
      "subtitle": "Credit Card has been linked",
      "time": "2 Month ago",
      "icon": Icons.credit_card,
      "date": "December 09, 2023",
    },
  ];

  NotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('notification.notification'.tr()),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: ListView.builder(
        padding: EdgeInsets.all(16.0.sp),
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final notification = notifications[index];

          final bool showDateHeader = index == 0 ||
              notifications[index - 1]["date"] != notification["date"];

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showDateHeader)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10.0),
                  child: Row(
                    children: [
                      Text(
                        notification["date"],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      5.horizontalSpace,
                      Expanded(
                        child: Container(
                          height: 1.sp,
                          width: double.infinity,
                          color: AppColors.neutralColor600,
                        ),
                      )
                    ],
                  ),
                ),
              NotificationTile(notification: notification),
            ],
          );
        },
      ),
    );
  }
}

class NotificationTile extends StatelessWidget {
  final Map<String, dynamic> notification;

  const NotificationTile({super.key, required this.notification});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      margin: const EdgeInsets.symmetric(vertical: 6.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: AppColors.neutralColor600),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12.sp,
        children: [
          Container(
            width: 40.sp,
            height: 40.sp,
            padding: EdgeInsets.all(8.sp),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                color: AppColors.primaryColor800.withValues(alpha: 0.1)),
            child: Icon(
              notification["icon"],
              // size: 24,
              color: AppColors.primaryColor800,
            ),
          ),
          // const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification["title"],
                    style: Styles.captionEmphasis
                        .copyWith(fontWeight: FontWeight.w700)),
                const SizedBox(height: 4),
                Text(notification["subtitle"],
                    style: Styles.captionEmphasis.copyWith(
                        fontWeight: FontWeight.w400,
                        color: AppColors.neutralColor600)),
              ],
            ),
          ),
          // const SizedBox(width: 8),
          Text(
            notification["time"],
            style: Styles.captionEmphasis.copyWith(
                fontWeight: FontWeight.w400, color: AppColors.neutralColor600),
          ),
        ],
      ),
    );
  }
}
