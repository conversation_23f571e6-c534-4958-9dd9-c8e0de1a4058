// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmployeeResponse _$EmployeeResponseFromJson(Map<String, dynamic> json) =>
    EmployeeResponse(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => EmployeeData.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as String?,
      error: json['error'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$EmployeeResponseToJson(EmployeeResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

EmployeeData _$EmployeeDataFromJson(Map<String, dynamic> json) => EmployeeData(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      branch: json['branch'] == null
          ? null
          : Branch.fromJson(json['branch'] as Map<String, dynamic>),
      department: json['department'] == null
          ? null
          : Department.fromJson(json['department'] as Map<String, dynamic>),
      position: json['position'] == null
          ? null
          : Position.fromJson(json['position'] as Map<String, dynamic>),
      shift: json['shift'] == null
          ? null
          : Shift.fromJson(json['shift'] as Map<String, dynamic>),
      salary: (json['salary'] as num?)?.toInt(),
      status: json['status'] as String?,
      contractType: json['contract_type'] as String?,
      maritalStatus: json['marital_status'] as String?,
      annualLeaveDays: (json['annual_leave_days'] as num?)?.toInt(),
      normalDays: (json['normal_days'] as num?)?.toInt(),
      sickDays: (json['sick_days'] as num?)?.toInt(),
      availableAnnualLeaveDays:
          (json['available_annual_leave_days'] as num?)?.toInt(),
      availableNormalDays: (json['available_normal_days'] as num?)?.toInt(),
      availableSickDays: (json['available_sick_days'] as num?)?.toInt(),
      contractStartDate: json['contract_start_date'] as String?,
      contractEndDate: json['contract_end_date'] as String?,
      accessToken: json['access_token'] as String?,
      role: json['role'] as String?,
      permissions: json['permissions'] as List<dynamic>?,
    );

Map<String, dynamic> _$EmployeeDataToJson(EmployeeData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'branch': instance.branch,
      'department': instance.department,
      'position': instance.position,
      'shift': instance.shift,
      'salary': instance.salary,
      'status': instance.status,
      'contract_type': instance.contractType,
      'marital_status': instance.maritalStatus,
      'annual_leave_days': instance.annualLeaveDays,
      'normal_days': instance.normalDays,
      'sick_days': instance.sickDays,
      'available_annual_leave_days': instance.availableAnnualLeaveDays,
      'available_normal_days': instance.availableNormalDays,
      'available_sick_days': instance.availableSickDays,
      'contract_start_date': instance.contractStartDate,
      'contract_end_date': instance.contractEndDate,
      'access_token': instance.accessToken,
      'role': instance.role,
      'permissions': instance.permissions,
    };

Branch _$BranchFromJson(Map<String, dynamic> json) => Branch(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
      country: json['country'] == null
          ? null
          : Country.fromJson(json['country'] as Map<String, dynamic>),
      city: json['city'] == null
          ? null
          : Name.fromJson(json['city'] as Map<String, dynamic>),
      address: json['address'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      annualLeaveDays: (json['annual_leave_days'] as num?)?.toInt(),
      normalDays: (json['normal_days'] as num?)?.toInt(),
      sickDays: (json['sick_days'] as num?)?.toInt(),
      long: json['long'] as String?,
      lat: json['lat'] as String?,
    );

Map<String, dynamic> _$BranchToJson(Branch instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'country': instance.country,
      'city': instance.city,
      'address': instance.address,
      'phone': instance.phone,
      'email': instance.email,
      'annual_leave_days': instance.annualLeaveDays,
      'normal_days': instance.normalDays,
      'sick_days': instance.sickDays,
      'long': instance.long,
      'lat': instance.lat,
    };

Name _$NameFromJson(Map<String, dynamic> json) => Name(
      en: json['en'] as String?,
      ar: json['ar'] as String?,
    );

Map<String, dynamic> _$NameToJson(Name instance) => <String, dynamic>{
      'en': instance.en,
      'ar': instance.ar,
    };

Country _$CountryFromJson(Map<String, dynamic> json) => Country(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
      code: json['code'] as String?,
    );

Map<String, dynamic> _$CountryToJson(Country instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
    };

Department _$DepartmentFromJson(Map<String, dynamic> json) => Department(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DepartmentToJson(Department instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };

Position _$PositionFromJson(Map<String, dynamic> json) => Position(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
      mangers: json['mangers'] == null
          ? null
          : Position.fromJson(json['mangers'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PositionToJson(Position instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'mangers': instance.mangers,
    };
