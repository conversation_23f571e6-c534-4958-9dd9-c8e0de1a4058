import 'package:json_annotation/json_annotation.dart';

import '../../../profile/data/model/profile_model.dart';

part 'employee_model.g.dart';

@JsonSerializable()
class EmployeeResponse {
  @JsonKey(name: 'data')
  List<EmployeeData>? data;

  @Json<PERSON>ey(name: 'status')
  String? status;

  @JsonKey(name: 'error')
  String? error;

  @JsonKey(name: 'code')
  int? code;

  EmployeeResponse({this.data, this.status, this.error, this.code});

  factory EmployeeResponse.fromJson(Map<String, dynamic> json) =>
      _$EmployeeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeResponseToJson(this);
}

@JsonSerializable()
class EmployeeData {
  int? id;
  String? name;
  String? email;
  String? phone;
  Branch? branch;
  Department? department;
  Position? position;
  Shift? shift;
  int? salary;
  String? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'contract_type')
  String? contractType;
  @Json<PERSON>ey(name: 'marital_status')
  String? maritalStatus;
  @Json<PERSON>ey(name: 'annual_leave_days')
  int? annualLeaveDays;
  @Json<PERSON>ey(name: 'normal_days')
  int? normalDays;
  @JsonKey(name: 'sick_days')
  int? sickDays;
  @JsonKey(name: 'available_annual_leave_days')
  int? availableAnnualLeaveDays;
  @JsonKey(name: 'available_normal_days')
  int? availableNormalDays;
  @JsonKey(name: 'available_sick_days')
  int? availableSickDays;
  @JsonKey(name: 'contract_start_date')
  String? contractStartDate;
  @JsonKey(name: 'contract_end_date')
  String? contractEndDate;
  @JsonKey(name: 'access_token')
  String? accessToken;
  String? role;
  List<dynamic>? permissions;

  EmployeeData({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.branch,
    this.department,
    this.position,
    this.shift,
    this.salary,
    this.status,
    this.contractType,
    this.maritalStatus,
    this.annualLeaveDays,
    this.normalDays,
    this.sickDays,
    this.availableAnnualLeaveDays,
    this.availableNormalDays,
    this.availableSickDays,
    this.contractStartDate,
    this.contractEndDate,
    this.accessToken,
    this.role,
    this.permissions,
  });

  factory EmployeeData.fromJson(Map<String, dynamic> json) =>
      _$EmployeeDataFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeDataToJson(this);
}

@JsonSerializable()
class Branch {
  int? id;
  Name? name;
  Country? country;
  Name? city;
  String? address;
  String? phone;
  String? email;
  @JsonKey(name: 'annual_leave_days')
  int? annualLeaveDays;
  @JsonKey(name: 'normal_days')
  int? normalDays;
  @JsonKey(name: 'sick_days')
  int? sickDays;
  String? long;
  String? lat;

  Branch({
    this.id,
    this.name,
    this.country,
    this.city,
    this.address,
    this.phone,
    this.email,
    this.annualLeaveDays,
    this.normalDays,
    this.sickDays,
    this.long,
    this.lat,
  });

  factory Branch.fromJson(Map<String, dynamic> json) => _$BranchFromJson(json);

  Map<String, dynamic> toJson() => _$BranchToJson(this);
}

@JsonSerializable()
class Name {
  String? en;
  String? ar;

  Name({this.en, this.ar});

  factory Name.fromJson(Map<String, dynamic> json) => _$NameFromJson(json);

  Map<String, dynamic> toJson() => _$NameToJson(this);
}

@JsonSerializable()
class Country {
  int? id;
  Name? name;
  String? code;

  Country({this.id, this.name, this.code});

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFromJson(json);

  Map<String, dynamic> toJson() => _$CountryToJson(this);
}

@JsonSerializable()
class Department {
  int? id;
  Name? name;

  Department({this.id, this.name});

  factory Department.fromJson(Map<String, dynamic> json) =>
      _$DepartmentFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentToJson(this);
}

@JsonSerializable()
class Position {
  int? id;
  Name? name;
  Position? mangers;

  Position({this.id, this.name, this.mangers});

  factory Position.fromJson(Map<String, dynamic> json) =>
      _$PositionFromJson(json);

  Map<String, dynamic> toJson() => _$PositionToJson(this);
}

