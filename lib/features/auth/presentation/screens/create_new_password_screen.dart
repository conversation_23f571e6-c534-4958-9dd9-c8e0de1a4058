import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/auth/business_logic/auth_cubit.dart';
import 'package:erp/features/auth/presentation/widgets/password_condition_item_widget.dart';
import 'package:erp/features/auth/presentation/widgets/show_change_password_bottom_sheet_widget.dart';
import 'package:erp/features/auth/presentation/widgets/step_forgot_password_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class CreateNewPasswordScreen extends StatelessWidget {
  const CreateNewPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              32.verticalSpace,
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'auth.createANew'.tr(),
                    style: Styles.heading2.copyWith(
                      color: AppColors.neutralColor1600,
                    ),
                  ),
                  Text(
                    'auth.newPassword'.tr(),
                    style: Styles.heading2.copyWith(
                      color: AppColors.primaryColor900,
                    ),
                  ),
                ],
              ),
              8.verticalSpace,
              Text(
                'auth.strongPassword'.tr(),
                textAlign: TextAlign.center,
                style: Styles.contentRegular.copyWith(
                  color: AppColors.neutralColor600,
                ),
              ),
              18.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.sp),
                child: Row(
                  children: [
                    StepForgotPasswordWidget(
                      imagePath: "assets/images/svgs/send_icon .svg",
                      label: 'auth.email'.tr(),
                      textColor: AppColors.neutralColor1600,
                    ),
                    StepForgotPasswordWidget(
                      imagePath: "assets/images/svgs/otp_icon .svg",
                      label: 'auth.otp'.tr(),
                      textColor: AppColors.neutralColor600,
                    ),
                    StepForgotPasswordWidget(
                      imagePath: "assets/images/svgs/password_icon.svg",
                      label: 'auth.password'.tr(),
                      textColor: AppColors.neutralColor600,
                    ),
                  ],
                ),
              ),
              12.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0.sp),
                child: SvgPicture.asset(
                  "assets/images/svgs/Container_new_password.svg",
                ),
              ),
              24.verticalSpace,
              Text(
                'auth.newPassword'.tr(),
                style: Styles.contentEmphasis,
              ),
              8.verticalSpace,
              BlocBuilder<AuthCubit, AuthState>(
                buildWhen: (previous, current) =>
                    current is TogglePasswordState,
                builder: (context, state) {
                  return CustomTextFormFieldWidget(
                    controller: context.read<AuthCubit>().passwordController,
                    hintText: 'auth.enterYourPassword'.tr(),
                    keyboardType: TextInputType.visiblePassword,
                    obscureText: context.read<AuthCubit>().isObscure,
                    suffixIcon: IconButton(
                      onPressed: () =>
                          context.read<AuthCubit>().toggleObscure(),
                      icon: Icon(context.read<AuthCubit>().isObscure
                          ? Icons.visibility
                          : Icons.visibility_off),
                    ),
                    borderRadius: AppConstants.borderRadius,
                  );
                },
              ),
              16.verticalSpace,
              Text(
                'auth.ConfirmNewPassword'.tr(),
                style: Styles.contentEmphasis,
              ),
              8.verticalSpace,
              BlocBuilder<AuthCubit, AuthState>(
                buildWhen: (previous, current) =>
                    current is TogglePasswordState,
                builder: (context, state) {
                  return CustomTextFormFieldWidget(
                    controller: context.read<AuthCubit>().confirmNewPassword,
                    hintText: 'auth.enterYourConfirm'.tr(),
                    keyboardType: TextInputType.visiblePassword,
                    obscureText: context.read<AuthCubit>().isObscure2,
                    suffixIcon: IconButton(
                      onPressed: () =>
                          context.read<AuthCubit>().toggleObscure2(),
                      icon: Icon(context.read<AuthCubit>().isObscure2
                          ? Icons.visibility
                          : Icons.visibility_off),
                    ),
                    borderRadius: AppConstants.borderRadius,
                  );
                },
              ),
              12.verticalSpace,
              PasswordConditionItem(
                iconPath: "assets/images/svgs/check_Icon_white.svg",
                text: 'auth.min8Characters'.tr(),
                textColor: AppColors.neutralColor600,
              ),
              12.verticalSpace,
              PasswordConditionItem(
                iconPath: "assets/images/svgs/check_Icon_white.svg",
                text: 'auth.2passwords'.tr(),
                textColor: AppColors.neutralColor600,
              ),
              12.verticalSpace,
              PasswordConditionItem(
                iconPath: "assets/images/svgs/check_Icon_red.svg",
                text: 'auth.leastoneCase'.tr(),
                textColor: AppColors.redColor100,
              ),
              12.verticalSpace,
              PasswordConditionItem(
                iconPath: "assets/images/svgs/check_Icon_green.svg",
                text: 'auth.leastoneCase'.tr(),
                textColor: AppColors.greenColor200,
              ),
              12.verticalSpace,
              PasswordConditionItem(
                iconPath: "assets/images/svgs/check_Icon_green.svg",
                text: 'auth.oneNumericCharacter'.tr(),
                textColor: AppColors.greenColor200,
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(20.sp),
        child: CustomButtonWidget(
          text: 'auth.submit'.tr(),
          width: double.infinity,
          elevation: 0,
          onPressed: () {
            showchangePasswordBottonSheet(context);
          },
        ),
      ),
    );
  }
}
