import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/auth/presentation/widgets/step_forgot_password_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class ForgotPasswordScreen extends StatelessWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.sp),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              32.verticalSpace,
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'auth.forgot'.tr(),
                    style: Styles.heading1.copyWith(
                      color: AppColors.neutralColor1600,
                    ),
                  ),
                  2.horizontalSpace,
                  Text(
                    'auth.Password'.tr(),
                    style: Styles.heading1.copyWith(
                      color: AppColors.primaryColor900,
                    ),
                  ),
                ],
              ),
              8.verticalSpace,
              Text(
                'auth.confirmationCode'.tr(),
                textAlign: TextAlign.center,
                style: Styles.contentRegular.copyWith(
                  color: AppColors.neutralColor600,
                ),
              ),
              18.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.sp),
                child: Row(
                  children: [
                    StepForgotPasswordWidget(
                      imagePath: "assets/images/svgs/send_icon .svg",
                      label: 'auth.email'.tr(),
                      textColor: AppColors.neutralColor1600,
                    ),
                    StepForgotPasswordWidget(
                      imagePath: "assets/images/svgs/otp_icon .svg",
                      label: "auth.otp".tr(),
                      textColor: AppColors.neutralColor600,
                    ),
                    StepForgotPasswordWidget(
                      imagePath: "assets/images/svgs/password_icon.svg",
                      label: 'auth.Password'.tr(),
                      textColor: AppColors.neutralColor600,
                    ),
                  ],
                ),
              ),
              12.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0.sp),
                child: SvgPicture.asset(
                  "assets/images/svgs/Container_forgot_password.svg",
                ),
              ),
              24.verticalSpace,
              Text(
                'auth.email'.tr(),
                style: Styles.contentEmphasis,
              ),
              8.verticalSpace,
              CustomTextFormFieldWidget(
                hintText: 'auth.enterYourEmail'.tr(),
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                borderRadius: AppConstants.borderRadius,
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(20.sp),
        child: CustomButtonWidget(
          text: 'auth.send'.tr(),
          width: double.infinity,
          elevation: 0,
          onPressed: () {
            context.pushNamed(Routes.otpForgetPasswordScreen);
          },
        ),
      ),
    );
  }
}
