import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class PasswordConditionItem extends StatelessWidget {
  final String iconPath;
  final String text;
  final Color textColor;

  const PasswordConditionItem({
    super.key,
    required this.iconPath,
    required this.text,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          iconPath,
          width: 20.w,
          height: 20.w,
        ),
        8.horizontalSpace,
        Text(
          text,
          style: Styles.captionRegular.copyWith(color: textColor),
        ),
      ],
    );
  }
}