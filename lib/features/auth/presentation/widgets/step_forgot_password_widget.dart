import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class StepForgotPasswordWidget extends StatelessWidget {
  final String imagePath;
  final String label;
  final Color textColor;

  const StepForgotPasswordWidget({
    super.key,
    required this.imagePath,
    required this.label,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          SvgPicture.asset(
            imagePath,
            width: 34.w,
            height: 34.h,
          ),
          8.verticalSpace,
          Text(
            label,
            style: Styles.captionRegular.copyWith(color: textColor),
          ),
        ],
      ),
    );
  }
}
