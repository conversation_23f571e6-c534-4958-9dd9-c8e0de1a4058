import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_text_rich_widget.dart';
import 'package:erp/features/auth/business_logic/auth_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResendCodeWidget extends StatelessWidget {
  const ResendCodeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final loginCubit = context.read<AuthCubit>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'auth.codeWillExpire'.tr(),
                style: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor1600,
                ),
                maxLines: 2,
              ),
            ),
            BlocBuilder<AuthCubit, AuthState>(
              buildWhen: (previous, current) => current is CountdownUpdated,
              builder: (context, state) {
                return CustomRichText(
                  text1: loginCubit.canResend
                      ? ''
                      : ' : ${loginCubit.seconds} ${'auth.seconds'.tr()}',
                  text2: loginCubit.canResend ? 'auth.resend'.tr() : '',
                  onTap2: loginCubit.canResend
                      ? () {
                          loginCubit.startTimer();
                        }
                      : null,
                );
              },
            ),
          ],
        ),
      ],
    );
  }
}
