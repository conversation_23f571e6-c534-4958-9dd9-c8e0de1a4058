import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/validation.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/auth/business_logic/auth_cubit.dart';
import 'package:erp/features/auth/presentation/widgets/resend_code_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';

class VerifyOtpFormWidget extends StatelessWidget {
  const VerifyOtpFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final loginCubit = context.read<AuthCubit>();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Column(
            children: [
              
              Pinput(
                controller: loginCubit.pinController,
                length: 4,
                autofocus: true,
                obscureText: false,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                pinAnimationType: PinAnimationType.fade,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(5),
                ],
                onChanged: (pin) {
                  
                },
                onCompleted: (pin) {
                  if (context.read<AuthCubit>().otpCode ==
                      context.read<AuthCubit>().pinController.text) {
                    context.read<AuthCubit>().userLogin();
                  } else {
                    showError('auth.wrongOTP'.tr(),);
                  }
                },
                defaultPinTheme: PinTheme(
                  width: 50.w,
                  height: 50.h,
                  textStyle: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.neutralColor200),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
                focusedPinTheme: PinTheme(
                  width: 50.w,
                  height: 50.h,
                  textStyle: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.primaryColor800),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
                validator: (pin) => AppValidator.validateOTP(pin),
                errorPinTheme: PinTheme(
                  width: 50.w,
                  height: 50.h,
                  textStyle: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.redColor100),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
                errorText: 'auth.wrongOTP'.tr(),
                errorTextStyle: Styles.contentEmphasis.copyWith(
                  color: AppColors.redColor100,
                ),
              ),
              SizedBox(height: 16.h),

              
              const ResendCodeWidget(),
            ],
          ),
        ),
      ],
    );
  }
}
