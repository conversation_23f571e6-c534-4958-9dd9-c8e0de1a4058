name: Android Fastlane with Firebase App Distribution Workflow

on:
  push:
    branches:
      - main

jobs:
  distribute_to_firebase:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout my repo code
      uses: actions/checkout@v3  # Latest stable version
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3  # Latest stable version
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Install Flutter
      uses: subosito/flutter-action@v2  # Current stable version
      with:
        channel: stable

    - name: Setup Ruby
      uses: ruby/setup-ruby@v1  # Latest stable version
      with:
        ruby-version: "3.4.1"
        bundler-cache: true
        working-directory: android
        
    - name: Build and Distribute App
      env:
        FIREBASE_CLI_TOKEN: 1//03h8-IY49pc1TCgYIARAAGAMSNwF-L9IrHhDF9T1ppSeFRgA4kdJvRiooE5iAYfUS6HMzaER7Zyo37-6exE6reIPnZ06f7F31IxE
      run: |
        bundle exec fastlane android firebase_distribution
      working-directory: android